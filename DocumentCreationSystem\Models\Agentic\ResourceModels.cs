namespace DocumentCreationSystem.Models.Agentic
{
    /// <summary>
    /// 资源需求
    /// </summary>
    public class ResourceDemand
    {
        /// <summary>
        /// 需求ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 资源类型
        /// </summary>
        public string ResourceType { get; set; } = string.Empty;

        /// <summary>
        /// 需求量
        /// </summary>
        public double Amount { get; set; } = 0.0;

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; } = 1;

        /// <summary>
        /// 需求时间窗口
        /// </summary>
        public TimeSpan TimeWindow { get; set; } = TimeSpan.FromHours(1);

        /// <summary>
        /// 最小需求量
        /// </summary>
        public double MinimumAmount { get; set; } = 0.0;

        /// <summary>
        /// 最大需求量
        /// </summary>
        public double MaximumAmount { get; set; } = double.MaxValue;

        /// <summary>
        /// 需求描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 相关计划
        /// </summary>
        public string PlanId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 资源利用率
    /// </summary>
    public class ResourceUtilization
    {
        /// <summary>
        /// 资源ID
        /// </summary>
        public string ResourceId { get; set; } = string.Empty;

        /// <summary>
        /// 资源类型
        /// </summary>
        public string ResourceType { get; set; } = string.Empty;

        /// <summary>
        /// 总容量
        /// </summary>
        public double TotalCapacity { get; set; } = 0.0;

        /// <summary>
        /// 已使用容量
        /// </summary>
        public double UsedCapacity { get; set; } = 0.0;

        /// <summary>
        /// 利用率百分比
        /// </summary>
        public double UtilizationPercentage => TotalCapacity > 0 ? (UsedCapacity / TotalCapacity) * 100 : 0;

        /// <summary>
        /// 可用容量
        /// </summary>
        public double AvailableCapacity => TotalCapacity - UsedCapacity;

        /// <summary>
        /// 峰值利用率
        /// </summary>
        public double PeakUtilization { get; set; } = 0.0;

        /// <summary>
        /// 平均利用率
        /// </summary>
        public double AverageUtilization { get; set; } = 0.0;

        /// <summary>
        /// 统计时间段
        /// </summary>
        public TimeSpan StatisticsPeriod { get; set; } = TimeSpan.FromHours(24);

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 负载均衡
    /// </summary>
    public class LoadBalancing
    {
        /// <summary>
        /// 均衡策略ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 均衡策略名称
        /// </summary>
        public string StrategyName { get; set; } = string.Empty;

        /// <summary>
        /// 目标利用率
        /// </summary>
        public double TargetUtilization { get; set; } = 0.8;

        /// <summary>
        /// 资源分配建议
        /// </summary>
        public List<ResourceAllocation> Allocations { get; set; } = new List<ResourceAllocation>();

        /// <summary>
        /// 均衡效果评估
        /// </summary>
        public double BalancingEffectiveness { get; set; } = 0.0;

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecutionTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 预期改善
        /// </summary>
        public Dictionary<string, double> ExpectedImprovements { get; set; } = new Dictionary<string, double>();
    }



    /// <summary>
    /// 资源调整
    /// </summary>
    public class ResourceAdjustment
    {
        /// <summary>
        /// 调整ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 调整类型
        /// </summary>
        public string AdjustmentType { get; set; } = string.Empty;

        /// <summary>
        /// 调整建议
        /// </summary>
        public List<AdjustmentRecommendation> Recommendations { get; set; } = new List<AdjustmentRecommendation>();

        /// <summary>
        /// 调整原因
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 预期效果
        /// </summary>
        public string ExpectedOutcome { get; set; } = string.Empty;

        /// <summary>
        /// 调整时间
        /// </summary>
        public DateTime AdjustmentTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 影响评估
        /// </summary>
        public Dictionary<string, object> ImpactAssessment { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 调整建议
    /// </summary>
    public class AdjustmentRecommendation
    {
        /// <summary>
        /// 资源ID
        /// </summary>
        public string ResourceId { get; set; } = string.Empty;

        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; set; } = 0.0;

        /// <summary>
        /// 建议值
        /// </summary>
        public double RecommendedValue { get; set; } = 0.0;

        /// <summary>
        /// 调整幅度
        /// </summary>
        public double AdjustmentAmount => RecommendedValue - CurrentValue;

        /// <summary>
        /// 调整理由
        /// </summary>
        public string Justification { get; set; } = string.Empty;

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; } = 0.0;
    }

    /// <summary>
    /// 资源效率
    /// </summary>
    public class ResourceEfficiency
    {
        /// <summary>
        /// 资源ID
        /// </summary>
        public string ResourceId { get; set; } = string.Empty;

        /// <summary>
        /// 效率评分
        /// </summary>
        public double EfficiencyScore { get; set; } = 0.0;

        /// <summary>
        /// 吞吐量
        /// </summary>
        public double Throughput { get; set; } = 0.0;

        /// <summary>
        /// 响应时间
        /// </summary>
        public TimeSpan ResponseTime { get; set; } = TimeSpan.Zero;

        /// <summary>
        /// 错误率
        /// </summary>
        public double ErrorRate { get; set; } = 0.0;

        /// <summary>
        /// 可用性
        /// </summary>
        public double Availability { get; set; } = 1.0;

        /// <summary>
        /// 成本效益
        /// </summary>
        public double CostEffectiveness { get; set; } = 0.0;

        /// <summary>
        /// 改进建议
        /// </summary>
        public List<string> ImprovementSuggestions { get; set; } = new List<string>();

        /// <summary>
        /// 评估时间
        /// </summary>
        public DateTime AssessmentTime { get; set; } = DateTime.Now;
    }
}
