using DocumentCreationSystem.Models.Agentic;

namespace DocumentCreationSystem.Services.Tools
{
    /// <summary>
    /// Agent工具服务接口
    /// </summary>
    public interface IAgentToolService
    {
        /// <summary>
        /// 执行工具
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <param name="parameters">参数</param>
        /// <returns>执行结果</returns>
        Task<string> ExecuteToolAsync(string toolName, Dictionary<string, object> parameters);

        /// <summary>
        /// 获取可用工具列表
        /// </summary>
        /// <returns>工具列表</returns>
        Task<List<AgentTool>> GetAvailableToolsAsync();

        /// <summary>
        /// 验证工具参数
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <param name="parameters">参数</param>
        /// <returns>验证结果</returns>
        Task<ToolValidationResult> ValidateToolParametersAsync(string toolName, Dictionary<string, object> parameters);

        /// <summary>
        /// 获取工具信息
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <returns>工具信息</returns>
        Task<AgentTool?> GetToolInfoAsync(string toolName);

        /// <summary>
        /// 注册工具
        /// </summary>
        /// <param name="tool">工具信息</param>
        /// <returns>是否成功</returns>
        Task<bool> RegisterToolAsync(AgentTool tool);

        /// <summary>
        /// 取消注册工具
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <returns>是否成功</returns>
        Task<bool> UnregisterToolAsync(string toolName);
    }

    /// <summary>
    /// Agent工具模型
    /// </summary>
    public class AgentTool
    {
        /// <summary>
        /// 工具名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 工具描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 工具类别
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 参数定义
        /// </summary>
        public List<ToolParameter> Parameters { get; set; } = new List<ToolParameter>();

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsAvailable { get; set; } = true;

        /// <summary>
        /// 工具版本
        /// </summary>
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// 工具标签
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();
    }

    /// <summary>
    /// 工具参数定义
    /// </summary>
    public class ToolParameter
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 参数类型
        /// </summary>
        public string Type { get; set; } = "string";

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool Required { get; set; } = false;

        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }

        /// <summary>
        /// 可选值
        /// </summary>
        public List<object>? AllowedValues { get; set; }
    }

    /// <summary>
    /// 工具验证结果
    /// </summary>
    public class ToolValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// 错误信息
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告信息
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 建议信息
        /// </summary>
        public List<string> Suggestions { get; set; } = new List<string>();
    }
}
