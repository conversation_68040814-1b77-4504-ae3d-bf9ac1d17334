using DocumentCreationSystem.Models.Memory;

namespace DocumentCreationSystem.Services.Memory
{
    /// <summary>
    /// 记忆服务接口
    /// </summary>
    public interface IMemoryService
    {
        /// <summary>
        /// 存储记忆
        /// </summary>
        /// <param name="memory">记忆对象</param>
        /// <returns>记忆ID</returns>
        Task<string> StoreMemoryAsync(Memory memory);

        /// <summary>
        /// 获取记忆
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <returns>记忆对象</returns>
        Task<Memory?> GetMemoryAsync(string memoryId);

        /// <summary>
        /// 搜索记忆
        /// </summary>
        /// <param name="query">搜索查询</param>
        /// <param name="limit">结果限制</param>
        /// <returns>记忆列表</returns>
        Task<List<Memory>> SearchMemoriesAsync(string query, int limit = 10);

        /// <summary>
        /// 删除记忆
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteMemoryAsync(string memoryId);

        /// <summary>
        /// 获取最近的记忆
        /// </summary>
        /// <param name="limit">结果限制</param>
        /// <returns>记忆列表</returns>
        Task<List<Memory>> GetRecentMemoriesAsync(int limit = 10);
    }

    /// <summary>
    /// 记忆模型 - 简化版本用于兼容
    /// </summary>
    public class Memory
    {
        /// <summary>
        /// 记忆ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 记忆内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 记忆类型
        /// </summary>
        public string Type { get; set; } = "general";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 重要性评分
        /// </summary>
        public double Importance { get; set; } = 0.5;

        /// <summary>
        /// 标签
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }
}
