using System.Windows;

namespace DocumentCreationSystem.Views.Dialogs
{
    /// <summary>
    /// PolishTextDialog.xaml 的交互逻辑
    /// </summary>
    public partial class PolishTextDialog : Window
    {
        public string SelectedStyle
        {
            get
            {
                if (GeneralStyle.IsChecked == true) return "通用";
                if (FormalStyle.IsChecked == true) return "正式";
                if (CasualStyle.IsChecked == true) return "轻松";
                if (LiteraryStyle.IsChecked == true) return "文学";
                if (TechnicalStyle.IsChecked == true) return "技术";
                return "通用";
            }
        }

        public PolishTextDialog()
        {
            InitializeComponent();
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
