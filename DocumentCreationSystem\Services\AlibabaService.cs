using System.Net.Http;
using System.Text;
using System.Text.Json;
using DocumentCreationSystem.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 阿里巴巴通义千问服务实现
/// </summary>
public class AlibabaService : IAIService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl;
    private readonly string? _apiKey;
    private readonly List<AIModel> _availableModels;
    private AIModel? _currentModel;

    public AlibabaService(IConfiguration configuration, ILogger logger)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = new HttpClient();
        
        _baseUrl = configuration["BaseUrl"] ?? "https://dashscope.aliyuncs.com/api/v1";
        _apiKey = configuration["ApiKey"];
        _availableModels = new List<AIModel>();
        
        ConfigureHttpClient();
        InitializeAvailableModels();
    }

    private void ConfigureHttpClient()
    {
        if (!string.IsNullOrWhiteSpace(_apiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
        }
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "DocumentCreationSystem/1.0");
        _httpClient.Timeout = TimeSpan.FromSeconds(120); // 120秒，符合API请求最佳实践
    }

    private void InitializeAvailableModels()
    {
        // 添加阿里巴巴通义千问模型
        _availableModels.AddRange(new[]
        {
            new AIModel { Id = "qwen-turbo", Name = "通义千问-Turbo", Provider = "Alibaba", IsAvailable = true },
            new AIModel { Id = "qwen-plus", Name = "通义千问-Plus", Provider = "Alibaba", IsAvailable = true },
            new AIModel { Id = "qwen-max", Name = "通义千问-Max", Provider = "Alibaba", IsAvailable = true },
            new AIModel { Id = "qwen-max-longcontext", Name = "通义千问-Max长文本", Provider = "Alibaba", IsAvailable = true },
            new AIModel { Id = "qwen2.5-72b-instruct", Name = "Qwen2.5-72B-Instruct", Provider = "Alibaba", IsAvailable = true },
            new AIModel { Id = "qwen2.5-32b-instruct", Name = "Qwen2.5-32B-Instruct", Provider = "Alibaba", IsAvailable = true },
            new AIModel { Id = "qwen2.5-14b-instruct", Name = "Qwen2.5-14B-Instruct", Provider = "Alibaba", IsAvailable = true },
            new AIModel { Id = "qwen2.5-7b-instruct", Name = "Qwen2.5-7B-Instruct", Provider = "Alibaba", IsAvailable = true }
        });

        // 从配置中获取自定义模型
        var customModel = _configuration["Model"];
        if (!string.IsNullOrEmpty(customModel) && !_availableModels.Any(m => m.Id == customModel))
        {
            _availableModels.Add(new AIModel 
            { 
                Id = customModel, 
                Name = customModel, 
                Provider = "Alibaba", 
                IsAvailable = true 
            });
        }
    }

    public async Task<List<AIModel>> GetAvailableModelsAsync()
    {
        try
        {
            // 检查模型可用性
            foreach (var model in _availableModels)
            {
                model.IsAvailable = await CheckModelAvailabilityAsync(model.Id);
            }

            return _availableModels.Where(m => m.IsAvailable).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取阿里巴巴模型列表失败");
            return _availableModels; // 返回默认列表
        }
    }

    private async Task<bool> CheckModelAvailabilityAsync(string modelId)
    {
        try
        {
            var url = $"{_baseUrl.TrimEnd('/')}/services/aigc/text-generation/generation";
            
            var request = new
            {
                model = modelId,
                input = new
                {
                    messages = new[]
                    {
                        new { role = "user", content = "test" }
                    }
                },
                parameters = new
                {
                    max_tokens = 1,
                    temperature = 0.1
                }
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            var response = await _httpClient.PostAsync(url, content, cts.Token);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, $"阿里巴巴模型连接测试失败: {modelId}");
            return false;
        }
    }

    public async Task<bool> SetCurrentModelAsync(string modelId)
    {
        var model = _availableModels.FirstOrDefault(m => m.Id == modelId);
        if (model == null)
        {
            _logger.LogWarning($"阿里巴巴模型不存在: {modelId}");
            return false;
        }

        if (!await CheckModelAvailabilityAsync(modelId))
        {
            _logger.LogWarning($"阿里巴巴模型不可用: {modelId}");
            return false;
        }

        _currentModel = model;
        _logger.LogInformation($"切换到阿里巴巴模型: {model.Name}");
        return true;
    }

    public AIModel? GetCurrentModel()
    {
        return _currentModel;
    }

    public async Task<string> GenerateTextAsync(string prompt, int maxTokens = 2000, float temperature = 0.7f)
    {
        if (_currentModel == null)
        {
            throw new InvalidOperationException("未设置当前阿里巴巴模型");
        }

        const int maxRetries = 3;
        var retryDelay = TimeSpan.FromSeconds(2);

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                var url = $"{_baseUrl.TrimEnd('/')}/services/aigc/text-generation/generation";
                
                var request = new
                {
                    model = _currentModel.Id,
                    input = new
                    {
                        messages = new[]
                        {
                            new { role = "user", content = prompt }
                        }
                    },
                    parameters = new
                    {
                        max_tokens = maxTokens,
                        temperature = temperature,
                        stream = false
                    }
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
                var response = await _httpClient.PostAsync(url, content, cts.Token);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"阿里巴巴API调用失败 (状态码: {response.StatusCode}): {errorContent}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var responseJson = JsonSerializer.Deserialize<JsonElement>(responseContent);
                
                if (responseJson.TryGetProperty("output", out var output) &&
                    output.TryGetProperty("text", out var text))
                {
                    return text.GetString() ?? "";
                }
                
                // 尝试新的响应格式
                if (responseJson.TryGetProperty("output", out var output2) &&
                    output2.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
                {
                    var firstChoice = choices[0];
                    if (firstChoice.TryGetProperty("message", out var message) &&
                        message.TryGetProperty("content", out var messageContent))
                    {
                        return messageContent.GetString() ?? "";
                    }
                }
                
                throw new Exception("阿里巴巴API响应格式异常");
            }
            catch (TaskCanceledException)
            {
                if (attempt == maxRetries)
                    throw new Exception("阿里巴巴请求超时");
                
                _logger.LogWarning($"阿里巴巴请求超时，第 {attempt} 次重试");
                await Task.Delay(retryDelay);
                retryDelay = TimeSpan.FromSeconds(retryDelay.TotalSeconds * 2); // 指数退避
            }
            catch (Exception ex)
            {
                if (attempt == maxRetries)
                    throw;
                
                _logger.LogWarning(ex, $"阿里巴巴文本生成失败，第 {attempt} 次重试");
                await Task.Delay(retryDelay);
                retryDelay = TimeSpan.FromSeconds(retryDelay.TotalSeconds * 2);
            }
        }
        
        throw new Exception("阿里巴巴文本生成失败，已达到最大重试次数");
    }

    public async Task<string> PolishTextAsync(string text, string style = "通用")
    {
        var prompt = $@"请对以下文本进行润色，润色风格：{style}

原文：
{text}

润色要求：
1. 保持原意不变
2. 提升语言表达的流畅性和准确性
3. 增强文本的可读性
4. 根据指定风格调整语言特色

请直接返回润色后的文本：";

        return await GenerateTextAsync(prompt, text.Length + 500, 0.7f);
    }

    public async Task<string> ExpandTextAsync(string text, int targetLength, string? context = null)
    {
        var contextInfo = !string.IsNullOrEmpty(context) ? $"\n\n上下文信息：\n{context}" : "";

        var prompt = $@"请对以下文本进行扩写，目标长度约{targetLength}字符。

原文：
{text}{contextInfo}

扩写要求：
1. 保持原文的核心内容和风格
2. 增加细节描述、背景信息或相关内容
3. 确保扩写内容与原文自然衔接
4. 达到目标长度要求

请直接返回扩写后的文本：";

        return await GenerateTextAsync(prompt, targetLength + 500, 0.7f);
    }

    public async Task<string> GenerateChapterAsync(string outline, string? context = null, int targetWordCount = 6500)
    {
        var contextInfo = !string.IsNullOrEmpty(context) ? $"\n\n上下文信息：\n{context}" : "";

        var prompt = $@"请根据以下大纲创作章节内容，目标字数约{targetWordCount}字。

章节大纲：
{outline}{contextInfo}

创作要求：
1. 严格按照大纲进行创作
2. 内容丰富，情节生动
3. 人物形象鲜明，对话自然
4. 场景描写细致，氛围营造到位
5. 达到目标字数要求

请开始创作章节正文：";

        return await GenerateTextAsync(prompt, Math.Max(targetWordCount + 2000, 8000), 0.8f);
    }

    public async Task<ConsistencyCheckResult> CheckConsistencyAsync(string currentText, string previousContext)
    {
        var prompt = $@"请检查以下当前文本与之前上下文的一致性，重点关注：
1. 人物性格和行为是否一致
2. 情节发展是否合理
3. 时间线是否正确
4. 设定是否矛盾

请简要分析并给出一致性评分（0-1），以及发现的问题和建议。

之前的上下文：
{previousContext}

当前文本：
{currentText}";

        try
        {
            var response = await GenerateTextAsync(prompt, 2000, 0.3f);

            // 简单的文本解析，提取一致性信息
            var isConsistent = !response.ToLower().Contains("不一致") && !response.ToLower().Contains("矛盾");
            var confidenceScore = ExtractConfidenceScore(response);

            return new ConsistencyCheckResult
            {
                IsConsistent = isConsistent,
                ConfidenceScore = confidenceScore,
                Issues = ExtractIssues(response),
                Suggestions = ExtractSuggestions(response)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "阿里巴巴一致性检查失败");
            return new ConsistencyCheckResult
            {
                IsConsistent = false,
                ConfidenceScore = 0.0f,
                Issues = new List<string> { $"检查过程出错: {ex.Message}" },
                Suggestions = new List<string> { "建议重新进行一致性检查" }
            };
        }
    }

    public async Task<string> GenerateOutlineAsync(string description, string outlineType)
    {
        var prompt = $"请根据以下描述生成{outlineType}大纲：\n{description}\n\n要求：\n1. 结构清晰，层次分明\n2. 内容丰富，具有可操作性\n3. 符合{outlineType}的特点和要求";
        return await GenerateTextAsync(prompt, 1500, 0.8f);
    }

    public async Task<List<CharacterInfo>> ExtractCharacterInfoAsync(string text)
    {
        var prompt = $"请从以下文本中提取角色信息，包括姓名、描述、关系等：\n{text}";
        var response = await GenerateTextAsync(prompt, 1000, 0.5f);
        
        // 简单解析响应，实际应用中可能需要更复杂的解析逻辑
        var characters = new List<CharacterInfo>();
        var lines = response.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        
        foreach (var line in lines)
        {
            if (line.Contains("姓名") || line.Contains("角色"))
            {
                characters.Add(new CharacterInfo
                {
                    Name = line.Trim(),
                    Description = "从文本中提取的角色信息"
                });
            }
        }

        return characters;
    }

    private float ExtractConfidenceScore(string response)
    {
        try
        {
            // 尝试从响应中提取置信度分数
            var lines = response.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains("置信度") || line.Contains("confidence"))
                {
                    var numbers = System.Text.RegularExpressions.Regex.Matches(line, @"0\.\d+|\d+\.\d+");
                    if (numbers.Count > 0 && float.TryParse(numbers[0].Value, out var score))
                    {
                        return Math.Min(1.0f, Math.Max(0.0f, score));
                    }
                }
            }
            return 0.8f; // 默认置信度
        }
        catch
        {
            return 0.8f;
        }
    }

    private List<string> ExtractIssues(string response)
    {
        var issues = new List<string>();
        try
        {
            var lines = response.Split('\n');
            bool inIssuesSection = false;

            foreach (var line in lines)
            {
                if (line.Contains("问题") || line.Contains("issue"))
                {
                    inIssuesSection = true;
                    continue;
                }

                if (inIssuesSection && (line.Contains("建议") || line.Contains("suggestion")))
                {
                    break;
                }

                if (inIssuesSection && !string.IsNullOrWhiteSpace(line))
                {
                    var cleanLine = line.Trim().TrimStart('-', '*', '•', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '.', ' ');
                    if (!string.IsNullOrWhiteSpace(cleanLine))
                    {
                        issues.Add(cleanLine);
                    }
                }
            }
        }
        catch
        {
            // 忽略解析错误
        }

        return issues;
    }

    private List<string> ExtractSuggestions(string response)
    {
        var suggestions = new List<string>();
        try
        {
            var lines = response.Split('\n');
            bool inSuggestionsSection = false;

            foreach (var line in lines)
            {
                if (line.Contains("建议") || line.Contains("suggestion"))
                {
                    inSuggestionsSection = true;
                    continue;
                }

                if (inSuggestionsSection && !string.IsNullOrWhiteSpace(line))
                {
                    var cleanLine = line.Trim().TrimStart('-', '*', '•', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '.', ' ');
                    if (!string.IsNullOrWhiteSpace(cleanLine))
                    {
                        suggestions.Add(cleanLine);
                    }
                }
            }
        }
        catch
        {
            // 忽略解析错误
        }

        return suggestions;
    }

    public async Task<string> TranslateTextAsync(string text, string sourceLanguage, string targetLanguage)
    {
        var prompt = $@"请将以下{sourceLanguage}文本翻译为{targetLanguage}，要求：
1. 保持原文的意思和语调
2. 使用自然流畅的{targetLanguage}表达
3. 只返回翻译结果，不要添加任何解释

原文：
{text}";

        return await GenerateTextAsync(prompt, text.Length * 2, 0.3f);
    }

    public async Task<string> SummarizeTextAsync(string text, int maxLength = 500)
    {
        var prompt = $@"请对以下文本进行总结，要求：
1. 总结长度不超过{maxLength}字
2. 保留核心要点和关键信息
3. 语言简洁明了
4. 只返回总结内容，不要添加任何解释

原文：
{text}";

        return await GenerateTextAsync(prompt, maxLength + 200, 0.5f);
    }

    public async Task<string> ContinueWritingAsync(string context, int targetLength = 500)
    {
        var prompt = $@"请基于以下上下文继续写作，要求：
1. 续写长度约{targetLength}字
2. 保持与上下文的风格一致
3. 内容连贯自然
4. 只返回续写内容，不要添加任何解释

上下文：
{context}";

        return await GenerateTextAsync(prompt, targetLength + 300, 0.8f);
    }
}
