using DocumentCreationSystem.Models.Agentic;

namespace DocumentCreationSystem.Services.AI
{
    /// <summary>
    /// AI服务扩展接口
    /// </summary>
    public interface IAIServiceExtensions
    {
        /// <summary>
        /// 生成文本
        /// </summary>
        /// <param name="prompt">提示词</param>
        /// <param name="maxTokens">最大令牌数</param>
        /// <param name="temperature">温度参数</param>
        /// <returns>生成的文本</returns>
        Task<string> GenerateTextAsync(string prompt, int maxTokens = 1000, float temperature = 0.7f);

        /// <summary>
        /// 分析意图
        /// </summary>
        /// <param name="input">输入文本</param>
        /// <returns>意图分析结果</returns>
        Task<IntentAnalysisResult> AnalyzeIntentAsync(string input);

        /// <summary>
        /// 评估上下文相关性
        /// </summary>
        /// <param name="context">上下文</param>
        /// <param name="query">查询</param>
        /// <returns>相关性评分</returns>
        Task<double> EvaluateContextRelevanceAsync(string context, string query);

        /// <summary>
        /// 生成执行计划
        /// </summary>
        /// <param name="goal">目标</param>
        /// <param name="context">上下文</param>
        /// <returns>执行计划</returns>
        Task<ExecutionPlan> GenerateExecutionPlanAsync(string goal, AgentContext context);

        /// <summary>
        /// 预测执行结果
        /// </summary>
        /// <param name="plan">执行计划</param>
        /// <param name="context">执行上下文</param>
        /// <returns>预测结果</returns>
        Task<ExecutionPrediction> PredictExecutionOutcomeAsync(ExecutionPlan plan, ExecutionContext context);
    }

    /// <summary>
    /// 意图分析结果
    /// </summary>
    public class IntentAnalysisResult
    {
        /// <summary>
        /// 主要意图
        /// </summary>
        public string PrimaryIntent { get; set; } = string.Empty;

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; } = 0.0;

        /// <summary>
        /// 次要意图
        /// </summary>
        public List<string> SecondaryIntents { get; set; } = new List<string>();

        /// <summary>
        /// 实体信息
        /// </summary>
        public Dictionary<string, object> Entities { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 情感倾向
        /// </summary>
        public string Sentiment { get; set; } = "neutral";
    }


}
