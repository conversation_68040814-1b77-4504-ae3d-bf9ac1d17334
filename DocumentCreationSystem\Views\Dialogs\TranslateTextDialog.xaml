<Window x:Class="DocumentCreationSystem.Views.Dialogs.TranslateTextDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="翻译文本" Height="300" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="选择翻译语言"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,24"
                   HorizontalAlignment="Center"/>

        <!-- 语言选择 -->
        <materialDesign:Card Grid.Row="1" Padding="16" Margin="0,0,0,16">
            <StackPanel>
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="源语言" 
                              Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                              Margin="0,0,0,8"/>
                    
                    <ComboBox x:Name="SourceLanguageComboBox" 
                             materialDesign:HintAssist.Hint="选择源语言"
                             SelectedIndex="0">
                        <ComboBoxItem Content="自动检测"/>
                        <ComboBoxItem Content="中文"/>
                        <ComboBoxItem Content="英文"/>
                        <ComboBoxItem Content="日文"/>
                        <ComboBoxItem Content="韩文"/>
                        <ComboBoxItem Content="法文"/>
                        <ComboBoxItem Content="德文"/>
                        <ComboBoxItem Content="西班牙文"/>
                        <ComboBoxItem Content="俄文"/>
                    </ComboBox>
                </StackPanel>

                <StackPanel>
                    <TextBlock Text="目标语言" 
                              Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                              Margin="0,0,0,8"/>
                    
                    <ComboBox x:Name="TargetLanguageComboBox" 
                             materialDesign:HintAssist.Hint="选择目标语言"
                             SelectedIndex="1">
                        <ComboBoxItem Content="中文"/>
                        <ComboBoxItem Content="英文"/>
                        <ComboBoxItem Content="日文"/>
                        <ComboBoxItem Content="韩文"/>
                        <ComboBoxItem Content="法文"/>
                        <ComboBoxItem Content="德文"/>
                        <ComboBoxItem Content="西班牙文"/>
                        <ComboBoxItem Content="俄文"/>
                    </ComboBox>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right">
            <Button Content="取消" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="0,0,8,0"
                   Click="Cancel_Click"/>
            
            <Button Content="开始翻译" 
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="OK_Click"/>
        </StackPanel>
    </Grid>
</Window>
