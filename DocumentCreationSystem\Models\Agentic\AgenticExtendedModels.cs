namespace DocumentCreationSystem.Models.Agentic
{
    /// <summary>
    /// 意图演化
    /// </summary>
    public class IntentEvolution
    {
        /// <summary>
        /// 演化ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 原始意图
        /// </summary>
        public string OriginalIntent { get; set; } = string.Empty;

        /// <summary>
        /// 当前意图
        /// </summary>
        public string CurrentIntent { get; set; } = string.Empty;

        /// <summary>
        /// 演化历史
        /// </summary>
        public List<IntentChange> EvolutionHistory { get; set; } = new List<IntentChange>();

        /// <summary>
        /// 演化原因
        /// </summary>
        public List<string> EvolutionReasons { get; set; } = new List<string>();

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; } = 0.0;
    }

    /// <summary>
    /// 意图变化
    /// </summary>
    public class IntentChange
    {
        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 变化前意图
        /// </summary>
        public string FromIntent { get; set; } = string.Empty;

        /// <summary>
        /// 变化后意图
        /// </summary>
        public string ToIntent { get; set; } = string.Empty;

        /// <summary>
        /// 变化原因
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 触发因素
        /// </summary>
        public List<string> Triggers { get; set; } = new List<string>();
    }

    /// <summary>
    /// 预测意图
    /// </summary>
    public class PredictedIntent
    {
        /// <summary>
        /// 意图内容
        /// </summary>
        public string Intent { get; set; } = string.Empty;

        /// <summary>
        /// 预测概率
        /// </summary>
        public double Probability { get; set; } = 0.0;

        /// <summary>
        /// 预测依据
        /// </summary>
        public List<string> Evidence { get; set; } = new List<string>();

        /// <summary>
        /// 预测时间窗口
        /// </summary>
        public TimeSpan TimeWindow { get; set; } = TimeSpan.FromHours(1);

        /// <summary>
        /// 相关上下文
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 上下文相关性
    /// </summary>
    public class ContextRelevance
    {
        /// <summary>
        /// 相关性评分
        /// </summary>
        public double Score { get; set; } = 0.0;

        /// <summary>
        /// 相关性类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 相关因素
        /// </summary>
        public List<string> Factors { get; set; } = new List<string>();

        /// <summary>
        /// 权重分布
        /// </summary>
        public Dictionary<string, double> Weights { get; set; } = new Dictionary<string, double>();

        /// <summary>
        /// 评估理由
        /// </summary>
        public string Reasoning { get; set; } = string.Empty;
    }

    /// <summary>
    /// 上下文反馈
    /// </summary>
    public class ContextFeedback
    {
        /// <summary>
        /// 反馈ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 反馈类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 反馈内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 反馈评分
        /// </summary>
        public double Rating { get; set; } = 0.0;

        /// <summary>
        /// 反馈时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 相关上下文
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 潜在风险
    /// </summary>
    public class PotentialRisk
    {
        /// <summary>
        /// 风险ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 风险名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 风险描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 风险级别
        /// </summary>
        public RiskLevel Level { get; set; } = RiskLevel.Medium;

        /// <summary>
        /// 发生概率
        /// </summary>
        public double Probability { get; set; } = 0.0;

        /// <summary>
        /// 影响程度
        /// </summary>
        public double Impact { get; set; } = 0.0;

        /// <summary>
        /// 缓解措施
        /// </summary>
        public List<string> MitigationStrategies { get; set; } = new List<string>();
    }

    /// <summary>
    /// 风险
    /// </summary>
    public class Risk
    {
        /// <summary>
        /// 风险ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 风险类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 风险描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 风险级别
        /// </summary>
        public RiskLevel Level { get; set; } = RiskLevel.Medium;

        /// <summary>
        /// 发生概率
        /// </summary>
        public double Probability { get; set; } = 0.0;

        /// <summary>
        /// 影响评估
        /// </summary>
        public string ImpactAssessment { get; set; } = string.Empty;

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 相关因素
        /// </summary>
        public List<string> RelatedFactors { get; set; } = new List<string>();
    }

    /// <summary>
    /// 风险概率
    /// </summary>
    public class RiskProbability
    {
        /// <summary>
        /// 风险ID
        /// </summary>
        public string RiskId { get; set; } = string.Empty;

        /// <summary>
        /// 概率值
        /// </summary>
        public double Value { get; set; } = 0.0;

        /// <summary>
        /// 计算方法
        /// </summary>
        public string CalculationMethod { get; set; } = string.Empty;

        /// <summary>
        /// 影响因素
        /// </summary>
        public Dictionary<string, double> InfluencingFactors { get; set; } = new Dictionary<string, double>();

        /// <summary>
        /// 置信区间
        /// </summary>
        public (double Lower, double Upper) ConfidenceInterval { get; set; } = (0.0, 1.0);

        /// <summary>
        /// 计算时间
        /// </summary>
        public DateTime CalculatedAt { get; set; } = DateTime.Now;
    }



    /// <summary>
    /// 风险评估结果
    /// </summary>
    public class RiskAssessmentResult
    {
        /// <summary>
        /// 评估ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 计划ID
        /// </summary>
        public string PlanId { get; set; } = string.Empty;

        /// <summary>
        /// 总体风险评分
        /// </summary>
        public double OverallRiskScore { get; set; } = 0.0;

        /// <summary>
        /// 识别的风险列表
        /// </summary>
        public List<Risk> IdentifiedRisks { get; set; } = new List<Risk>();

        /// <summary>
        /// 风险分析
        /// </summary>
        public string RiskAnalysis { get; set; } = string.Empty;

        /// <summary>
        /// 建议措施
        /// </summary>
        public List<string> Recommendations { get; set; } = new List<string>();

        /// <summary>
        /// 评估时间
        /// </summary>
        public DateTime AssessedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 评估者
        /// </summary>
        public string AssessedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 执行预测结果
    /// </summary>
    public class ExecutionPrediction
    {
        /// <summary>
        /// 预测成功率
        /// </summary>
        public double SuccessProbability { get; set; } = 0.5;

        /// <summary>
        /// 预期结果
        /// </summary>
        public string ExpectedOutcome { get; set; } = string.Empty;

        /// <summary>
        /// 潜在风险
        /// </summary>
        public List<string> PotentialRisks { get; set; } = new List<string>();

        /// <summary>
        /// 建议改进
        /// </summary>
        public List<string> Recommendations { get; set; } = new List<string>();

        /// <summary>
        /// 预计执行时间
        /// </summary>
        public TimeSpan EstimatedDuration { get; set; } = TimeSpan.Zero;

        /// <summary>
        /// 预测时间
        /// </summary>
        public DateTime PredictedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; } = 0.0;
    }

    /// <summary>
    /// 风险状态
    /// </summary>
    public class RiskStatus
    {
        /// <summary>
        /// 风险ID
        /// </summary>
        public string RiskId { get; set; } = string.Empty;

        /// <summary>
        /// 当前状态
        /// </summary>
        public string CurrentStatus { get; set; } = string.Empty;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 趋势方向
        /// </summary>
        public string TrendDirection { get; set; } = string.Empty;

        /// <summary>
        /// 警报列表
        /// </summary>
        public List<string> Alerts { get; set; } = new List<string>();

        /// <summary>
        /// 状态详情
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 风险监控
    /// </summary>
    public class RiskMonitoring
    {
        /// <summary>
        /// 执行ID
        /// </summary>
        public string ExecutionId { get; set; } = string.Empty;

        /// <summary>
        /// 监控的风险列表
        /// </summary>
        public List<Risk> MonitoredRisks { get; set; } = new List<Risk>();

        /// <summary>
        /// 监控状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 最后检查时间
        /// </summary>
        public DateTime LastChecked { get; set; } = DateTime.Now;

        /// <summary>
        /// 活跃警报
        /// </summary>
        public List<string> ActiveAlerts { get; set; } = new List<string>();

        /// <summary>
        /// 监控指标
        /// </summary>
        public Dictionary<string, object> Metrics { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 建议行动
        /// </summary>
        public List<string> RecommendedActions { get; set; } = new List<string>();
    }
}
