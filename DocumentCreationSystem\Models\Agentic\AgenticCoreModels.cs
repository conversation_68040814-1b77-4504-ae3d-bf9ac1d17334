namespace DocumentCreationSystem.Models.Agentic
{
    /// <summary>
    /// 战术情况
    /// </summary>
    public class TacticalSituation
    {
        /// <summary>
        /// 情况ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 情况描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 紧急程度
        /// </summary>
        public UrgencyLevel Urgency { get; set; } = UrgencyLevel.Normal;

        /// <summary>
        /// 可用资源
        /// </summary>
        public Dictionary<string, object> AvailableResources { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 约束条件
        /// </summary>
        public List<string> Constraints { get; set; } = new List<string>();

        /// <summary>
        /// 上下文信息
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 战术选项
    /// </summary>
    public class TacticalOption
    {
        /// <summary>
        /// 选项ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 选项名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 选项描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 预期效果
        /// </summary>
        public string ExpectedOutcome { get; set; } = string.Empty;

        /// <summary>
        /// 成功概率
        /// </summary>
        public double SuccessProbability { get; set; } = 0.5;

        /// <summary>
        /// 资源需求
        /// </summary>
        public Dictionary<string, object> ResourceRequirements { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 风险评估
        /// </summary>
        public double RiskLevel { get; set; } = 0.5;

        /// <summary>
        /// 执行步骤
        /// </summary>
        public List<string> ExecutionSteps { get; set; } = new List<string>();
    }

    /// <summary>
    /// 选项评估结果
    /// </summary>
    public class OptionEvaluation
    {
        /// <summary>
        /// 选项ID
        /// </summary>
        public string OptionId { get; set; } = string.Empty;

        /// <summary>
        /// 总体评分
        /// </summary>
        public double OverallScore { get; set; } = 0.0;

        /// <summary>
        /// 可行性评分
        /// </summary>
        public double FeasibilityScore { get; set; } = 0.0;

        /// <summary>
        /// 效果评分
        /// </summary>
        public double EffectivenessScore { get; set; } = 0.0;

        /// <summary>
        /// 风险评分
        /// </summary>
        public double RiskScore { get; set; } = 0.0;

        /// <summary>
        /// 成本评分
        /// </summary>
        public double CostScore { get; set; } = 0.0;

        /// <summary>
        /// 评估理由
        /// </summary>
        public string Reasoning { get; set; } = string.Empty;

        /// <summary>
        /// 建议改进
        /// </summary>
        public List<string> Improvements { get; set; } = new List<string>();
    }

    /// <summary>
    /// 决策执行结果
    /// </summary>
    public class DecisionExecution
    {
        /// <summary>
        /// 执行ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 决策ID
        /// </summary>
        public string DecisionId { get; set; } = string.Empty;

        /// <summary>
        /// 执行状态
        /// </summary>
        public ExecutionStatus Status { get; set; } = ExecutionStatus.NotStarted;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        public string Result { get; set; } = string.Empty;

        /// <summary>
        /// 执行日志
        /// </summary>
        public List<string> ExecutionLog { get; set; } = new List<string>();

        /// <summary>
        /// 遇到的问题
        /// </summary>
        public List<string> Issues { get; set; } = new List<string>();

        /// <summary>
        /// 性能指标
        /// </summary>
        public Dictionary<string, object> Metrics { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 快速响应结果
    /// </summary>
    public class QuickResponse
    {
        /// <summary>
        /// 响应ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 响应动作
        /// </summary>
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// 响应时间
        /// </summary>
        public TimeSpan ResponseTime { get; set; } = TimeSpan.Zero;

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; } = 0.0;

        /// <summary>
        /// 后续建议
        /// </summary>
        public List<string> FollowUpActions { get; set; } = new List<string>();

        /// <summary>
        /// 风险警告
        /// </summary>
        public List<string> RiskWarnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 紧急情况
    /// </summary>
    public class UrgentSituation
    {
        /// <summary>
        /// 情况ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 情况描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 紧急级别
        /// </summary>
        public UrgencyLevel Level { get; set; } = UrgencyLevel.High;

        /// <summary>
        /// 截止时间
        /// </summary>
        public DateTime Deadline { get; set; } = DateTime.Now.AddMinutes(5);

        /// <summary>
        /// 影响范围
        /// </summary>
        public string ImpactScope { get; set; } = string.Empty;

        /// <summary>
        /// 相关上下文
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 紧急程度枚举
    /// </summary>
    public enum UrgencyLevel
    {
        Low = 1,
        Normal = 2,
        High = 3,
        Critical = 4,
        Emergency = 5
    }


}
