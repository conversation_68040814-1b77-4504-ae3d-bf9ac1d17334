using System;
using System.Windows;
using System.Windows.Controls;

namespace DocumentCreationSystem.Views.Dialogs
{
    /// <summary>
    /// ExpandTextDialog.xaml 的交互逻辑
    /// </summary>
    public partial class ExpandTextDialog : Window
    {
        public string Requirements => RequirementsTextBox.Text;
        public int TargetLength
        {
            get
            {
                if (int.TryParse(TargetLengthTextBox.Text, out int length))
                    return Math.Max(100, length); // 最少100字
                return 500; // 默认500字
            }
        }

        public ExpandTextDialog()
        {
            InitializeComponent();
            RequirementsTextBox.Focus();
        }

        private void PresetButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string requirement)
            {
                RequirementsTextBox.Text = requirement;
            }
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(Requirements))
            {
                MessageBox.Show("请输入扩写要求。", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                RequirementsTextBox.Focus();
                return;
            }

            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
