using System.Windows;
using System.Windows.Controls;

namespace DocumentCreationSystem.Views.Dialogs
{
    /// <summary>
    /// TranslateTextDialog.xaml 的交互逻辑
    /// </summary>
    public partial class TranslateTextDialog : Window
    {
        public string SourceLanguage
        {
            get
            {
                if (SourceLanguageComboBox.SelectedItem is ComboBoxItem item)
                    return item.Content.ToString() ?? "自动检测";
                return "自动检测";
            }
        }

        public string TargetLanguage
        {
            get
            {
                if (TargetLanguageComboBox.SelectedItem is ComboBoxItem item)
                    return item.Content.ToString() ?? "英文";
                return "英文";
            }
        }

        public TranslateTextDialog()
        {
            InitializeComponent();
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            if (SourceLanguage == TargetLanguage && SourceLanguage != "自动检测")
            {
                MessageBox.Show("源语言和目标语言不能相同。", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
