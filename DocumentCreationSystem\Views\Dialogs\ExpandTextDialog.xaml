<Window x:Class="DocumentCreationSystem.Views.Dialogs.ExpandTextDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="按要求扩写" Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="按要求扩写文本"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,24"
                   HorizontalAlignment="Center"/>

        <!-- 扩写要求输入 -->
        <materialDesign:Card Grid.Row="1" Padding="16" Margin="0,0,0,16">
            <StackPanel>
                <TextBlock Text="扩写要求" 
                          Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                          Margin="0,0,0,8"/>
                
                <TextBox x:Name="RequirementsTextBox"
                        materialDesign:HintAssist.Hint="请输入具体的扩写要求，如：增加细节描述、情感表达、场景渲染等"
                        AcceptsReturn="True"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto"
                        Height="120"
                        Margin="0,0,0,16"/>

                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="目标长度：" 
                              VerticalAlignment="Center" 
                              Margin="0,0,8,0"/>
                    
                    <TextBox x:Name="TargetLengthTextBox"
                            Text="500"
                            Width="80"
                            materialDesign:HintAssist.Hint="字数"
                            VerticalAlignment="Center"
                            Margin="0,0,8,0"/>
                    
                    <TextBlock Text="字" VerticalAlignment="Center"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- 预设选项 -->
        <materialDesign:Card Grid.Row="2" Padding="16" Margin="0,0,0,16">
            <StackPanel>
                <TextBlock Text="快速选择" 
                          Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                          Margin="0,0,0,8"/>
                
                <WrapPanel>
                    <Button Content="增加细节描述" 
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,8,8"
                           Click="PresetButton_Click"
                           Tag="增加更多细节描述，丰富内容的具体性和生动性"/>
                    
                    <Button Content="情感表达" 
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,8,8"
                           Click="PresetButton_Click"
                           Tag="增强情感表达，让内容更有感染力和共鸣"/>
                    
                    <Button Content="场景渲染" 
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,8,8"
                           Click="PresetButton_Click"
                           Tag="增加场景描写，营造更好的氛围和画面感"/>
                    
                    <Button Content="逻辑扩展" 
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,8,8"
                           Click="PresetButton_Click"
                           Tag="扩展逻辑推理，增加论证过程和思考深度"/>
                </WrapPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="3" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right">
            <Button Content="取消" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="0,0,8,0"
                   Click="Cancel_Click"/>
            
            <Button Content="开始扩写" 
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="OK_Click"/>
        </StackPanel>
    </Grid>
</Window>
