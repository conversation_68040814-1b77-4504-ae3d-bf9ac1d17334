using System;
using System.Windows;

namespace DocumentCreationSystem.Views.Dialogs
{
    /// <summary>
    /// SummaryResultDialog.xaml 的交互逻辑
    /// </summary>
    public partial class SummaryResultDialog : Window
    {
        private readonly string _originalText;
        private readonly string _summaryText;

        public bool ShouldInsertToDocument { get; private set; } = false;

        public SummaryResultDialog(string originalText, string summaryText)
        {
            InitializeComponent();
            
            _originalText = originalText;
            _summaryText = summaryText;
            
            // 设置内容
            OriginalTextBlock.Text = _originalText;
            SummaryTextBlock.Text = _summaryText;
        }

        private void CopySummary_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Clipboard.SetText(_summaryText);
                MessageBox.Show("总结内容已复制到剪贴板。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InsertToDocument_Click(object sender, RoutedEventArgs e)
        {
            ShouldInsertToDocument = true;
            DialogResult = true;
            Close();
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
