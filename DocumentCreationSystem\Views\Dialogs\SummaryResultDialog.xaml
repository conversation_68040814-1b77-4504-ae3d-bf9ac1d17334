<Window x:Class="DocumentCreationSystem.Views.Dialogs.SummaryResultDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="内容总结" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="内容总结结果"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,24"
                   HorizontalAlignment="Center"/>

        <!-- 内容区域 -->
        <TabControl Grid.Row="1" Margin="0,0,0,16">
            <TabItem Header="总结结果">
                <materialDesign:Card Padding="16">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <TextBlock x:Name="SummaryTextBlock"
                                  TextWrapping="Wrap"
                                  Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  LineHeight="24"/>
                    </ScrollViewer>
                </materialDesign:Card>
            </TabItem>
            
            <TabItem Header="原文内容">
                <materialDesign:Card Padding="16">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <TextBlock x:Name="OriginalTextBlock"
                                  TextWrapping="Wrap"
                                  Style="{StaticResource MaterialDesignBody2TextBlock}"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  LineHeight="20"/>
                    </ScrollViewer>
                </materialDesign:Card>
            </TabItem>
        </TabControl>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right">
            <Button Content="复制总结" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="0,0,8,0"
                   Click="CopySummary_Click">
                <Button.ToolTip>
                    <ToolTip Content="复制总结内容到剪贴板"/>
                </Button.ToolTip>
            </Button>
            
            <Button Content="插入文档" 
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Margin="0,0,8,0"
                   Click="InsertToDocument_Click">
                <Button.ToolTip>
                    <ToolTip Content="将总结内容插入到文档中"/>
                </Button.ToolTip>
            </Button>
            
            <Button Content="关闭" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Close_Click"/>
        </StackPanel>
    </Grid>
</Window>
