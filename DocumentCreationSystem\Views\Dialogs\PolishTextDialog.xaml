<Window x:Class="DocumentCreationSystem.Views.Dialogs.PolishTextDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="润色文本" Height="350" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="选择润色风格"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,24"
                   HorizontalAlignment="Center"/>

        <!-- 润色风格选择 -->
        <materialDesign:Card Grid.Row="1" Padding="16" Margin="0,0,0,16">
            <StackPanel>
                <TextBlock Text="润色风格" 
                          Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                          Margin="0,0,0,16"/>

                <RadioButton x:Name="GeneralStyle" 
                           Content="通用润色" 
                           IsChecked="True"
                           Margin="0,0,0,12"
                           GroupName="PolishStyle"/>
                
                <TextBlock Text="提升语言流畅性和可读性，适合大多数文本"
                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          Margin="24,0,0,16"/>

                <RadioButton x:Name="FormalStyle" 
                           Content="正式文体" 
                           Margin="0,0,0,12"
                           GroupName="PolishStyle"/>
                
                <TextBlock Text="适合商务文档、学术论文等正式场合"
                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          Margin="24,0,0,16"/>

                <RadioButton x:Name="CasualStyle" 
                           Content="轻松文体" 
                           Margin="0,0,0,12"
                           GroupName="PolishStyle"/>
                
                <TextBlock Text="适合日常交流、博客文章等轻松场合"
                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          Margin="24,0,0,16"/>

                <RadioButton x:Name="LiteraryStyle" 
                           Content="文学风格" 
                           Margin="0,0,0,12"
                           GroupName="PolishStyle"/>
                
                <TextBlock Text="增强文学性和艺术性，适合创意写作"
                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          Margin="24,0,0,16"/>

                <RadioButton x:Name="TechnicalStyle" 
                           Content="技术文档" 
                           Margin="0,0,0,12"
                           GroupName="PolishStyle"/>
                
                <TextBlock Text="适合技术说明、操作手册等专业文档"
                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          Margin="24,0,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right">
            <Button Content="取消" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="0,0,8,0"
                   Click="Cancel_Click"/>
            
            <Button Content="开始润色" 
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="OK_Click"/>
        </StackPanel>
    </Grid>
</Window>
